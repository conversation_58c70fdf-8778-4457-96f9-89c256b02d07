<?php

// Test script to verify wallet changes
require_once 'bootstrap/app.php';

use Thorne\Wallet\Models\WalletTransaction;
use Thorne\Wallet\Models\WalletTransfer;

echo "Testing wallet reference generation...\n";

// Test 1: Reference generation with UUID
$reference1 = wallet_generate_reference('TRF');
$reference2 = wallet_generate_reference('TRF');

echo "Reference 1: $reference1\n";
echo "Reference 2: $reference2\n";
echo "References are unique: " . ($reference1 !== $reference2 ? 'YES' : 'NO') . "\n\n";

// Test 2: Check if references use UUID format (should be 8 characters after removing dashes)
$randomPart1 = substr($reference1, strrpos($reference1, '-') + 1);
$randomPart2 = substr($reference2, strrpos($reference2, '-') + 1);

echo "Random part 1 length: " . strlen($randomPart1) . " (should be 8)\n";
echo "Random part 2 length: " . strlen($randomPart2) . " (should be 8)\n";
echo "Random parts are different: " . ($randomPart1 !== $randomPart2 ? 'YES' : 'NO') . "\n\n";

// Test 3: Verify database structure changes
echo "Testing database structure...\n";

try {
    // Check if transfer_id column exists in wallet_transactions
    $hasTransferId = \Schema::hasColumn('wallet_transactions', 'transfer_id');
    echo "wallet_transactions has transfer_id column: " . ($hasTransferId ? 'YES' : 'NO') . "\n";
    
    // Check if total_amount column exists in wallet_transactions
    $hasTotalAmount = \Schema::hasColumn('wallet_transactions', 'total_amount');
    echo "wallet_transactions has total_amount column: " . ($hasTotalAmount ? 'YES' : 'NO') . "\n";
    
    // Check if net_amount column still exists (should not)
    $hasNetAmount = \Schema::hasColumn('wallet_transactions', 'net_amount');
    echo "wallet_transactions still has net_amount column: " . ($hasNetAmount ? 'YES (BAD)' : 'NO (GOOD)') . "\n";
    
    // Check wallet_transfers table
    $transferHasTotalAmount = \Schema::hasColumn('wallet_transfers', 'total_amount');
    echo "wallet_transfers has total_amount column: " . ($transferHasTotalAmount ? 'YES' : 'NO') . "\n";
    
    $transferHasNetAmount = \Schema::hasColumn('wallet_transfers', 'net_amount');
    echo "wallet_transfers still has net_amount column: " . ($transferHasNetAmount ? 'YES (BAD)' : 'NO (GOOD)') . "\n";
    
} catch (Exception $e) {
    echo "Error checking database structure: " . $e->getMessage() . "\n";
}

echo "\nTest completed!\n";
